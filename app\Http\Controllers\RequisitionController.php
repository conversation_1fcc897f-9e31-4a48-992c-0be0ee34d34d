<?php

namespace App\Http\Controllers;

use App\Models\LogAktivitas;
use App\Models\Notification;
use App\Models\Site;
use Illuminate\Http\Request;
use App\Models\Requisition;
use App\Models\RequisitionDetail;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\StockTransaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RequisitionController extends Controller
{
    public function index(Request $request)
    {
        try {
            $perPage = 15; // 15 items per page
            $page = $request->input('page', 1);

            // Set today's date as default
            $today = date('Y-m-d');

            // Base query for requisitions
            $baseQuery = Requisition::with('site')
                ->select('requisition_id', 'site_id', 'status', 'requisition_date', 'title', 'notes')
                ->orderBy('requisition_date', 'desc');

            // Clone the query for today's data
            $todayQuery = clone $baseQuery;
            $todayQuery->whereDate('requisition_date', $today);
            $siteid = Site::all();

            // Check if we have data for today
            $hasDataToday = $todayQuery->exists();
            $usedLastDate = false;
            $lastDate = $today;

            // If no data for today, find the most recent date with data
            if (!$hasDataToday) {
                $lastRequisition = $baseQuery->first();

                if ($lastRequisition) {
                    $lastDate = date('Y-m-d', strtotime($lastRequisition->requisition_date));
                    $usedLastDate = true;
                }
            }

            // Final query with appropriate date filter
            $query = clone $baseQuery;
            $query->whereDate('requisition_date', $lastDate);

            // Get total count for pagination
            $total = $query->count();

            // Get paginated data
            $requisitionsData = $query->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get()
                ->map(function ($item) {
                    return [
                        'requisition_id' => $item->requisition_id,
                        'site_name' => $item->site->site_name,
                        'status' => $item->status,
                        'requisition_date' => $item->requisition_date,
                        'title' => $item->title,
                        'notes' => $item->notes,
                    ];
                });

            // Create pagination data
            $paginationData = [
                'current_page' => (int) $page,
                'per_page' => $perPage,
                'last_page' => ceil($total / $perPage),
                'total' => $total
            ];

            return view('warehouse.confirmation', [
                'requisitions' => $requisitionsData,
                'paginationData' => $paginationData,
                'siteid' => $siteid,
                'usedLastDate' => $usedLastDate,
                'lastDate' => $lastDate
            ]);
        } catch (\Exception $e) {
            Log::error('Error in index: ' . $e->getMessage());
            return view('warehouse.confirmation')->with('error', 'Terjadi kesalahan saat memuat data.');
        }
    }

    public function getDetails($id)
    {
        try {
            DB::beginTransaction();

            $details = RequisitionDetail::with('part')
                ->where('requisition_id', $id)
                ->get()
                ->map(function ($detail) {
                    return [
                        'part_name' => $detail->part->part_name,
                        'quantity' => $detail->quantity,
                        'quantity_confirm' => $detail->quantity_confirm,
                        'quantity_send' => $detail->quantity_send,
                        'status_details' => $detail->status_details,
                        'notes' => $detail->notes,
                    ];
                });

            DB::commit();
            return response()->json($details);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in getDetails: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan saat memuat detail.'], 500);
        }
    }

    public function updateDetail(Request $request)
    {
        // Add more descriptive validation messages
        $messages = [
            'requisition_id.required' => 'ID Pengajuan harus diisi',
            'requisition_id.exists' => 'ID Pengajuan tidak valid',
            'part_name.required' => 'Nama Part harus diisi',
            'status_details.required' => 'Status harus diisi',
            'status_details.in' => 'Status tidak valid. Pilih status yang tersedia',
            'qusent.required' => 'Jumlah pengiriman harus diisi',
            'qusent.integer' => 'Jumlah pengiriman harus berupa angka',
            'qusent.min' => 'Jumlah pengiriman minimal 1',
            'surat_jalan.max' => 'Ukuran file lampiran maksimal 2MB',
        ];

        // Validate basic required fields first
        $request->validate([
            'requisition_id' => 'required|exists:requisitions,requisition_id',
            'part_name' => 'required|string',
            'status_details' => 'required|string|in:intransit,pending,dikirim sebagian,selesai',
        ], $messages);

        try {
            DB::beginTransaction();

            $part = Part::where('part_name', $request->part_name)->firstOrFail();
            $detail = RequisitionDetail::where([
                'requisition_id' => $request->requisition_id,
                'part_code' => $part->part_code,
            ])->firstOrFail();

            $requisition = Requisition::find($detail->requisition_id);

            // Special handling for 'pending' status - only update status and ignore other fields
            if ($request->status_details === 'pending') {
                // Only update the status field
                $detail->update([
                    'status_details' => 'pending',
                ]);

                // Create activity log for status change
                LogAktivitas::create([
                    'site_id' => session('site_id'),
                    'name' => session('name'),
                    'action' => 'Update Status Pengajuan',
                    'description' => "Admin HO " . session('name') . " Mengubah Status Pengajuan: " . $request->part_name . " menjadi Pending",
                    'table' => "Data Part",
                    'ip_address' => $request->ip(),
                ]);

                // Update parent requisition status
                $this->updateRequisitionStatus($requisition);

                DB::commit();
                return response()->json([
                    'success' => true,
                    'message' => 'Status pengajuan berhasil diubah menjadi Pending.',
                    'new_status' => $detail->status_details,
                ]);
            }

            // For other statuses, validate additional required fields
            $request->validate([
                'qusent' => 'required|integer|min:1',
                'notes_ho' => 'nullable|string|max:255',
                'surat_jalan' => 'nullable|file|max:2048|mimes:pdf,jpg,jpeg,png',
            ], $messages);

            // Validate stock availability before processing
            $inventory = PartInventory::where([
                'part_code' => $part->part_code,
                'site_id' => session('site_id')
            ])->first();

            if (!$inventory) {
                throw new \Exception('Stok part tidak tersedia di lokasi ini', 404);
            }

            if ($inventory->stock_quantity < $request->qusent) {
                throw new \Exception("Stok tidak mencukupi. Stok tersedia: {$inventory->stock_quantity}", 400);
            }

            // keamanan transaksi
            if ($request->qusent <= 0) {
                throw new \Exception('Quantity sent must be greater than 0', 400);
            }

            // Check source inventory if sending parts
            if (in_array($request->status_details, ['intransit', 'dikirim sebagian'])) {
                $sourceInventory = PartInventory::where('part_code', $part->part_code)
                    ->where('site_id', session('site_id'))
                    ->lockForUpdate()
                    ->first();

                if (!$sourceInventory || $sourceInventory->stock_quantity < $request->qusent) {
                    throw new \Exception('Stok tidak mencukupi untuk pengiriman', 400);
                }

                // Update source inventory
                $sourceInventory->stock_quantity -= $request->qusent;
                $sourceInventory->save();
            }

            // Handle file upload
            $filePath = null;
            if ($request->hasFile('surat_jalan')) {
                $file = $request->file('surat_jalan');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $file->move(public_path('storage/surat_jalan'), $fileName);
                $filePath = 'surat_jalan/' . $fileName;
            }

            // Update detail
            $detail->update([
                'status_details' => $request->status_details,
                'notes_ho' => $request->notes_ho,
                'quantity' => $detail->quantity - $request->qusent,
                'quantity_send' => $detail->quantity_send + $request->qusent,
                'confirmation_date' => now(),
            ]);

            // Create transaction if needed
            if (in_array($request->status_details, ['intransit', 'dikirim sebagian'])) {
                $transaction = StockTransaction::create([
                    'part_code' => $detail->part_code,
                    'transaction_type' => 'outbound',
                    'destination_siteid' => $requisition->site_id,
                    'quantity_sent' => $request->qusent,
                    'status' => 'intransit',
                    'notes' => $request->notes_ho,
                    'surat_jalan_path' => $filePath,
                    'requisition_details_id' => $detail->requisition_details_id,
                ]);

                // Create notification
                Notification::create([
                    'title' => 'Pengiriman Part',
                    'message' => "Part {$part->part_name} sedang dalam pengiriman sebanyak {$request->qusent}",
                    'type' => 'stock',
                    'routes' => 'sites.instock.index',
                    'site_id' => $requisition->site_id, // Destination site (correct)
                    'from_site' => session('site_id'), // Source site (warehouse/admin HO)
                    'is_read' => false,
                ]);
            }

            // Create activity log
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Update Data Pengajuan',
                'description' => "Admin HO " . session('name') . " Mengubah Data Pengajuan: " . $request->part_name . " dengan status " . $request->status_details,
                'table' => "Requisition Details",
                'ip_address' => $request->ip(),
            ]);

            $this->updateRequisitionStatus($requisition);

            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Detail pengajuan berhasil diperbarui.',
                'new_status' => $detail->status_details,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in updateDetail: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function approveAll(Request $request, $id)
    {
        try {
            DB::beginTransaction();

            $requisition = Requisition::findOrFail($id);

            RequisitionDetail::where('requisition_id', $id)
                ->update([
                    'status_details' => 'disetujui',
                    'updated_at' => now()
                ]);

            $requisition->update([
                'status' => 'selesai',
                'confirmation_date' => now()
            ]);

            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menyetujui Semua Data Pengajuan',
                'description' => "Admin HO " . session('name') . " Menyetujui semua Data Pengajuan dengan judul: " . $requisition->title,
                'table' => "Requisitions",
                'ip_address' => $request->ip(),
            ]);

            Notification::create([
                'title' => 'Pembaharuan Pengajuan Part',
                'message' => 'Admin Ho mengubah status pengajuan : ' . $requisition->title,
                'type' => 'part_request',
                'routes' => 'pengajuan.index',
                'site_id' => $requisition->site_id, // Destination site (correct)
                'from_site' => session('site_id'), // Source site (warehouse/admin HO)
                'is_read' => false,
            ]);

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Berhasil menyetujui semua detail pengajuan.']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in approveAll: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    private function updateRequisitionStatus($requisition)
    {
        try {
            DB::beginTransaction();

            $pendingExists = RequisitionDetail::where('requisition_id', $requisition->requisition_id)
                ->whereIn('status_details', ['pending', 'tolak', 'dikirim sebagian', 'intransit'])
                ->exists();

            $newStatus = $pendingExists ? 'pending' : 'selesai';
            $requisition->update(['status' => $newStatus]);

            DB::commit();
            return $newStatus;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in updateRequisitionStatus: ' . $e->getMessage());
            throw $e;
        }
    }

    public function getcount()
    {
        try {
            DB::beginTransaction();

            $jumlah = Requisition::where('status', ['pending', 'diajukan'])->count();

            DB::commit();
            return response()->json(['jumlah' => $jumlah]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in getcount: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan saat menghitung pengajuan.'], 500);
        }
    }

    public function getRequisitions(Request $request)
    {
        try {
            DB::beginTransaction();

            $perPage = $request->input('per_page', 15); // Default to 15 items per page
            $page = $request->input('page', 1);
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $siteid = $request->input('siteid');
            $useLastDate = $request->input('use_last_date', true); // Default to true to always show last date data if no data found

            // Base query for requisitions
            $baseQuery = Requisition::with('site')
                ->select('requisition_id', 'site_id', 'status', 'requisition_date', 'title', 'notes')
                ->orderBy('requisition_date', 'desc');

            // Clone the query for potential last date lookup
            $query = clone $baseQuery;

            // If no date range provided, default to today's date
            if (!$startDate && !$endDate) {
                $today = date('Y-m-d');
                $startDate = $today;
                $endDate = $today;
            }

            // Apply date filters
            if ($startDate && $endDate) {
                $query->whereDate('requisition_date', '>=', $startDate)
                    ->whereDate('requisition_date', '<=', $endDate);
            }
            if($siteid){
                $query->where('site_id', $siteid);
            }

            // Check if we have data for the selected filters
            $hasData = $query->exists();
            $usedLastDate = false;
            $lastDate = null;

            // If no data found and we should use the last date with data
            if (!$hasData && $useLastDate) {
                // Find the last date that has data
                $lastRequisition = $baseQuery->first();

                if ($lastRequisition) {
                    $lastDate = date('Y-m-d', strtotime($lastRequisition->requisition_date));

                    // Reset the query to use the last date
                    $query = clone $baseQuery;
                    $query->whereDate('requisition_date', $lastDate);
                    $usedLastDate = true;
                }
            }

            // Get total count for pagination
            $total = $query->count();

            // Get paginated data
            $requisitionsData = $query->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get()
                ->map(function ($item) {
                    return [
                        'requisition_id' => $item->requisition_id,
                        'site_name' => $item->site->site_name,
                        'status' => $item->status,
                        'requisition_date' => $item->requisition_date,
                        'title' => $item->title,
                        'notes' => $item->notes,
                    ];
                });

            DB::commit();
            return response()->json([
                'requisitions' => $requisitionsData,
                'current_page' => (int) $page,
                'per_page' => (int) $perPage,
                'last_page' => ceil($total / $perPage),
                'total' => $total,
                'usedLastDate' => $usedLastDate,
                'lastDate' => $lastDate,
                'startDate' => $startDate,
                'endDate' => $endDate
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in getRequisitions: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan saat memuat data: ' . $e->getMessage()], 500);
        }
    }
}







