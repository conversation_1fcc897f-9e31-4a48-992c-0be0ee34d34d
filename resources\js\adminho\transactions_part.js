import Swal from "sweetalert2";

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 15; // 15 items per page for transactions table

document.addEventListener("DOMContentLoaded", function () {
    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderPagination(data) {
        try {
            const paginationContainer = document.getElementById('transactions-pagination');
            if (!paginationContainer) {
                console.error('Pagination container not found');
                return;
            }

            paginationContainer.innerHTML = '';

            if (data.last_page > 1) {
                const pagination = document.createElement('ul');
                pagination.className = 'pagination pagination-rounded  justify-content-center';

                // Previous button
                if (data.current_page > 1) {
                    pagination.appendChild(createPaginationItem(data.current_page - 1, '\u00ab'));
                }

                // Page numbers
                for (let i = 1; i <= data.last_page; i++) {
                    pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
                }

                // Next button
                if (data.current_page < data.last_page) {
                    pagination.appendChild(createPaginationItem(data.current_page + 1, '\u00bb'));
                }

                paginationContainer.appendChild(pagination);

                // Add event listeners to pagination links
                paginationContainer.querySelectorAll('.page-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const page = parseInt(this.dataset.page);
                        currentPage = page;
                        const siteId = document.getElementById('siteFilter').value;
                        loadTransactions(page, siteId);
                    });
                });
            }

            // Update global pagination data
            window.transactionsPaginationData = data;
        } catch (error) {
            console.error('Error rendering pagination:', error);
        }
    }

    // Function to populate site filter dropdown
    function populateSiteFilter(sites) {
        const siteFilter = document.getElementById('siteFilter');
        if (!siteFilter) {
            console.error('Site filter dropdown not found');
            return;
        }

        // Clear existing options except the first one
        while (siteFilter.options.length > 1) {
            siteFilter.remove(1);
        }

        // Add site options
        sites.forEach(site => {
            const option = document.createElement('option');
            option.value = site.site_id;
            option.textContent = site.site_name;
            siteFilter.appendChild(option);
        });

        // Add event listener for site filter
        siteFilter.addEventListener('change', function() {
            loadTransactions(1, this.value); // Reset to page 1 when filter changes
        });
    }

    // Pastikan semua fungsi berada di dalam scope DOMContentLoaded
    const loadTransactions = (page = 1, siteId = '') => {
        currentPage = page;
        const tableBody = document.querySelector("#transaction-table tbody");

        // Show loading indicator
        tableBody.innerHTML = '<tr><td colspan="10" class="text-center">.</td></tr>';

        fetch(`/warehouse/transactions?page=${page}&per_page=${itemsPerPage}&site_id=${siteId}`, {
            headers: {
                "X-Requested-With": "XMLHttpRequest",
            },
        })
            .then((response) => response.json())
            .then((data) => {
                // Populate site filter dropdown if it's the first load
                if (page === 1 && !siteId) {
                    populateSiteFilter(data.sites);
                }

                const transactions = data.transactions;
                tableBody.innerHTML = "";

                if (transactions.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="10" class="text-center">No transactions found</td></tr>';
                    return;
                }

                let i = ((page - 1) * itemsPerPage) + 1; // Calculate starting index based on page
                transactions.forEach((transaction) => {
                    console.log(transaction);
                    
                    let actionButtons = "";
                    if (transaction.status === "intransit") {
                        actionButtons = `<button class="delete-button btn btn-sm btn-danger" data-transaction-id="${transaction.stock_transaction_id}">Hapus</button>`;
                    } else if (transaction.status === "return") {
                        actionButtons = `<button id="return-button-${transaction.stock_transaction_id}" class="return-button btn btn-sm btn-warning text-black" data-transaction-id="${transaction.stock_transaction_id}">Edit Return</button>`;
                    } else if (transaction.status === "hilang") {
                        actionButtons = `<button id="hilang-button-${transaction.stock_transaction_id}" class="hilang-button btn btn-sm btn-secondary" data-transaction-id="${transaction.stock_transaction_id}">Edit Hilang</button>`;
                    } else {
                        actionButtons = `<button id="resolve-button-${transaction.stock_transaction_id}" class="resolve-button btn btn-sm btn-primary" data-transaction-id="${transaction.stock_transaction_id}">Edit Status</button>`;
                    }
                    const row = document.createElement("tr");
                    row.innerHTML = `
                        <td>${i++}</td>
                        <td>${transaction.part.part_code}</td>
                        <td>${transaction.part.part_name}</td>
                        <td>${transaction.site.site_name}</td>
                         <td>${formatDate(transaction.transaction_date)}</td>
                        <td>${transaction.quantity_sent}</td>
                        <td>${
                            transaction.quantity_received
                                ? transaction.quantity_received
                                : 0
                        }</td>
                        <td>${transaction.status}</td>
                        <td>${
                            transaction.quantity_discrepancy
                                ? transaction.quantity_discrepancy
                                : 0
                        }</td>
                        <td>
                        ${actionButtons}
                        </td>
                    `;
                    tableBody.appendChild(row);
                });

                // Tambahkan event listener untuk tombol hapus setelah tabel dimuat
                const deleteButtons =
                    document.querySelectorAll(".delete-button");
                deleteButtons.forEach((button) => {
                    button.addEventListener("click", function () {
                        const transactionId = this.dataset.transactionId;
                        Swal.fire({
                            title: "Apakah Anda yakin?",
                            text: "Anda tidak akan dapat mengembalikan ini!",
                            icon: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#3085d6",
                            cancelButtonColor: "#d33",
                            confirmButtonText: "Ya, Hapus!",
                            cancelButtonText: "Batal",
                        }).then((result) => {
                            if (result.isConfirmed) {
                                deleteTransaction(transactionId);
                            }
                        });
                    });
                });

                // Tambahkan event listener untuk tombol edit setelah tabel dimuat
                const editButtons = document.querySelectorAll(".edit-button");
                editButtons.forEach((button) => {
                    button.addEventListener("click", function () {
                        const transactionId = this.dataset.transactionId;
                        // TODO: Implementasikan logika untuk mengedit status transaksi
                        // Misalnya, tampilkan modal, redirect ke halaman edit, dll.
                        Swal.fire(
                            "Fitur Belum Tersedia",
                            `Edit status untuk transaksi ID: ${transactionId}`,
                            "info"
                        ); // Ganti dengan logika sebenarnya
                    });
                });

                // Render pagination
                renderPagination({
                    current_page: data.current_page,
                    per_page: data.per_page,
                    last_page: data.last_page,
                    total: data.total
                });
            })
            .catch((error) => {
                console.error("Error:", error);
                Swal.fire(
                    "Error!",
                    "Gagal memuat transaksi: " + error,
                    "error"
                );
            });
    };

    const deleteTransaction = (transactionId) => {
        fetch(`/warehouse/transactions/${transactionId}`, {
            method: "DELETE",
            headers: {
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"), // Pastikan Anda memiliki meta tag ini di head HTML Anda
                "X-Requested-With": "XMLHttpRequest",
            },
        })
            .then((response) => {
                if (response.ok) {
                    // Refresh tabel setelah berhasil menghapus
                    loadTransactions(1, '');
                    Swal.fire("Berhasil!", "Transaksi berhasil dihapus.", "success"); // Berikan feedback ke pengguna
                } else {
                    console.error("Gagal menghapus transaksi.");
                    Swal.fire("Gagal!", "Gagal menghapus transaksi.", "error");
                }
            })
            .catch((error) => {
                console.error("Error:", error);
                Swal.fire(
                    "Error!",
                    "Terjadi kesalahan saat menghapus transaksi: " + error,
                    "error"
                );
            });
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const options = {
            year: "numeric",
            month: "long",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        };
        return date.toLocaleDateString("id-ID", options);
    };

    const showResolutionForm = (transactionId, type = null) => {
        const resolveForm = document.getElementById("resolve-form");
        resolveForm.style.display = "block";
        document.getElementById("resolve-transaction-id").value = transactionId;

        // Set the status dropdown based on the button type
        const statusDropdown = document.getElementById("discrepancy_status");
        if (type === 'return') {
            // Pre-select 'return' option
            statusDropdown.value = "return";
            // Optionally disable the dropdown to prevent changing to other statuses
            // statusDropdown.disabled = true;
        } else if (type === 'hilang') {
            // Pre-select 'hilang' option
            statusDropdown.value = "hilang";
            // Optionally disable the dropdown to prevent changing to other statuses
            // statusDropdown.disabled = true;
        } else {
            // For other cases, reset the dropdown
            statusDropdown.value = "investigation";
            // statusDropdown.disabled = false;
        }
    };

    const hideResolutionForm = () => {
        const resolveForm = document.getElementById("resolve-form");
        resolveForm.style.display = "none";
    };

    const submitResolution = () => {
        const form = document.getElementById("resolve-form-inner");
        const formData = new FormData(form);
        const transactionId = document.getElementById(
            "resolve-transaction-id"
        ).value;

        document
            .querySelectorAll(".error")
            .forEach((el) => (el.textContent = ""));

        // Get CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        Swal.fire({
            title: "Konfirmasi",
            text: "Anda yakin ingin menyimpan perubahan ini?",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, Simpan!",
            cancelButtonText: "Batal",
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/warehouse/transactions/${transactionId}/resolve`, {
                    method: "POST",
                    headers: {
                        "X-CSRF-TOKEN": csrfToken,
                        "X-Requested-With": "XMLHttpRequest",
                        "Accept": "application/json"
                    },
                    body: formData, // Send the FormData object directly
                })
                    .then((response) => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then((data) => {
                        if (data.success) {
                            Swal.fire("Berhasil!", data.message, "success");
                            hideResolutionForm();
                            loadTransactions(currentPage, document.getElementById('siteFilter').value);
                        } else {
                            Swal.fire("Gagal!", data.message, "error");
                        }
                    })
                    .catch((error) => {
                        console.error("Error:", error);
                        Swal.fire(
                            "Error!",
                            "Terjadi kesalahan: " + error,
                            "error"
                        );
                    });
            }
        });
    };

    // Event Listeners
    document
        .querySelector("#transaction-table tbody")
        .addEventListener("click", (event) => {
            if (event.target.classList.contains("resolve-button")) {
                const transactionId = event.target.dataset.transactionId;
                showResolutionForm(transactionId);
            } else if (event.target.classList.contains("return-button")) {
                const transactionId = event.target.dataset.transactionId;
                showResolutionForm(transactionId, 'return');
            } else if (event.target.classList.contains("hilang-button")) {
                const transactionId = event.target.dataset.transactionId;
                showResolutionForm(transactionId, 'hilang');
            }
        });

    document
        .getElementById("resolve-form-inner")
        .addEventListener("click", (event) => {
            if (
                event.target.tagName === "BUTTON" &&
                event.target.type === "submit"
            ) {
                event.preventDefault(); // Prevent form submission
                submitResolution();
            }
            if (
                event.target.tagName === "BUTTON" &&
                event.target.type === "button"
            ) {
                event.preventDefault();
                hideResolutionForm();
            }
        });

    // Initial load with page 1
    loadTransactions(1, '');
});