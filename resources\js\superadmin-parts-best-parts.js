/**
 * Superadmin Parts - Best Parts
 * Handles best parts display and filtering on the parts page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize date range change event listeners
    initDateRangeListeners();

    // Initialize tab functionality for best parts section
    initBestPartsTabs();

    // Initialize best parts settings
    initBestPartsSettings();

    // Initialize automatic filter change listeners
    initAutoFilterListeners();

});

/**
 * Initialize automatic filter change listeners
 */
function initAutoFilterListeners() {
    // Get date range form elements
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');

    // Add event listeners for automatic refresh when date inputs change
    if (startDateInput) {
        startDateInput.addEventListener('change', function() {
            loadBestPartsDataWithCurrentFilters();
        });
    }

    if (endDateInput) {
        endDateInput.addEventListener('change', function() {
            loadBestPartsDataWithCurrentFilters();
        });
    }

    // Add event listeners to category tabs for immediate refresh
    const categoryTabs = document.querySelectorAll('.category-tab');
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Small delay to allow tab switching animation
            setTimeout(() => {
                // loadBestPartsDataWithCurrentFilters();
            }, 100);
        });
    });
}

/**
 * Load best parts data with current filter values
 */
function loadBestPartsDataWithCurrentFilters() {
    // Get current date range values
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const siteid = document.getElementById('site_idselect').value;

    const startDate = startDateInput ? startDateInput.value : '';
    const endDate = endDateInput ? endDateInput.value : '';

    // Load data with current filters
    loadBestPartsData('',siteid, startDate, endDate);
}

/**
 * Initialize date range listeners
 */
function initDateRangeListeners() {
    // Get date range form elements
    const dateRangeForm = document.getElementById('dateRangeForm');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const searchBtn = document.getElementById('searchBtn');

    if (dateRangeForm) {
        dateRangeForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Use AJAX instead of page reload for better UX
            loadBestPartsDataWithCurrentFilters();
        });
    }

    // Add event listener to search button if it exists
    if (searchBtn) {
        searchBtn.addEventListener('click', function(e) {
            e.preventDefault();
            loadBestPartsDataWithCurrentFilters();
        });
    }

    // Note: Auto-refresh functionality for date inputs is handled in initAutoFilterListeners()

    // Add event listeners to division filter tabs
    const divisionTabs = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');
    divisionTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Get the division from the tab
            const division = this.getAttribute('data-division');

            // Load best parts data for this division
            loadBestPartsData(division);
        });
    });
}

/**
 * Initialize tabs functionality for best parts section
 */
function initBestPartsTabs() {
    // Get all category tabs
    const categoryTabs = document.querySelectorAll('.category-tab');

    // Get all category panes
    const categoryPanes = document.querySelectorAll('.category-pane');

    // Show the first category pane by default
    if (categoryPanes.length > 0) {
        categoryPanes[0].style.display = 'block';
    }

    // Add click event listeners to category tabs
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Get the category from the tab
            const category = this.getAttribute('data-category');

            // Remove active class from all tabs
            categoryTabs.forEach(t => t.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Hide all category panes
            categoryPanes.forEach(pane => {
                pane.style.display = 'none';
            });

            // Show the selected category pane
            const selectedPane = document.getElementById(`category-${category}`);
            if (selectedPane) {
                selectedPane.style.display = 'block';
            }
            // Load best parts data for this category
            loadBestPartsData(category);
        });
    });
}

/**
 * Load best parts data for a specific division
 *
 * @param {string} division - The division to load data for
 * @param {string} site - The site filter to load data for
 * @param {string} startDate - Start date override
 * @param {string} endDate - End date override
 */
function loadBestPartsData(division = '',site = null, startDate = '', endDate = '') {
    // Show loading indicator specifically for best parts section
    showBestPartsLoading();

    // Get date range values if not provided
    if (!startDate || !endDate) {
        const startDateInput = document.getElementById('start-date');
        const endDateInput = document.getElementById('end-date');
        
        startDate = startDateInput ? startDateInput.value : '';
        endDate = endDateInput ? endDateInput.value : '';
    }
    if (!site) {
        const siteid2 = document.getElementById('site_idselect').value;
        if (siteid2) {
            site = siteid2
        }
    }

    // Build query parameters
    let queryParams = new URLSearchParams();

    // Add date range parameters if available
    if (startDate && endDate) {
        queryParams.append('start_date', startDate);
        queryParams.append('end_date', endDate);
    }

    // Add division filter if provided
    if (division) {
        queryParams.append('division', division);
    }
    if (site) {
        queryParams.append('site', site);
    }
    
    fetch(`/superadmin/best-parts-data?${queryParams.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {

            // Update the best parts content for each division
            updateBestPartsContent(data, division);

            // Hide loading indicator
            hideBestPartsLoading();

            // Show success feedback (optional)
            showSuccessFeedback();
        })
        .catch(error => {
            console.error('Error loading best parts data:', error);

            // Show user-friendly error message
            showErrorMessage('Gagal memuat data part terbaik. Silakan periksa koneksi internet dan coba lagi.');

            // Hide loading indicator
            hideBestPartsLoading();
        });
}

/**
 * Update best parts content with the loaded data
 *
 * @param {Object} data - Best parts data from the server
 * @param {string} selectedDivision - The selected division
 */
function updateBestPartsContent(data, selectedDivision = '') {
    // Define the divisions we want to update
    const divisions = ['AC', 'TYRE', 'FABRIKASI'];

    // If a specific division is selected, only update that one
    if (selectedDivision && divisions.includes(selectedDivision.toUpperCase())) {
        updateDivisionContent(selectedDivision.toUpperCase(), data[selectedDivision.toUpperCase()]);
    } else {
        // Otherwise update all divisions
        divisions.forEach(division => {
            if (data[division]) {
                updateDivisionContent(division, data[division]);
            }
        });
    }
}

/**
 * Update content for a specific division
 *
 * @param {string} division - The division to update
 * @param {Object} divisionData - The data for this division
 */
function updateDivisionContent(division, divisionData) {
    // Get the category pane for this division
    const categoryPane = document.getElementById(`category-${division}`);
    if (!categoryPane) return;

    // Get the table body for this division
    const tableBody = categoryPane.querySelector('tbody');
    if (!tableBody) return;

    // Get the empty state element for this division
    const emptyState = categoryPane.querySelector('.empty-state');

    // Update the total revenue display
    const totalRevenueElement = categoryPane.querySelector('h6[style*="color"]');
    if (totalRevenueElement && divisionData && divisionData.total_revenue_with_ppn !== undefined) {
        totalRevenueElement.textContent = `Rp ${formatNumber(divisionData.total_revenue_with_ppn)}`;
    }

    // Check if we have data for this division
    if (divisionData && divisionData.count > 0 && divisionData.items && divisionData.items.length > 0) {
        // Show the table and hide the empty state
        const tableResponsive = tableBody.closest('.table-responsive');
        if (tableResponsive) {
            tableResponsive.style.display = 'block';
        }
        if (emptyState) {
            emptyState.style.display = 'none';
        }

        // Clear existing rows
        tableBody.innerHTML = '';

        // Add new rows
        divisionData.items.forEach(part => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${part.part_name}</td>
                <td>${part.total_quantity}</td>
                <td>Rp ${formatNumber(part.total_value)}</td>
                <td>${part.contribution_percent}%</td>
            `;
            tableBody.appendChild(row);
        });
    } else {
        // Hide the table and show the empty state
        const tableResponsive = tableBody.closest('.table-responsive');
        if (tableResponsive) {
            tableResponsive.style.display = 'none';
        }
        if (emptyState) {
            emptyState.style.display = 'block';
        }
    }

    // Also update the revenue card for this division
    const revenueCard = document.querySelector(`.revenue-card[data-part-type="${division}"] h4`);
    if (revenueCard && divisionData && divisionData.total_revenue_with_ppn !== undefined) {
        revenueCard.textContent = `Rp ${formatNumber(divisionData.total_revenue_with_ppn)}`;
    }
}

/**
 * Initialize best parts settings
 */
function initBestPartsSettings() {
    const saveBestPartsSettingsBtn = document.getElementById('saveBestPartsSettings');
    if (!saveBestPartsSettingsBtn) return;

    saveBestPartsSettingsBtn.addEventListener('click', function() {
        
        // Show loading indicator for best parts section
        showBestPartsLoading();

        // Get settings values
        const limit = document.getElementById('bestPartsLimit').value;
        const sortByValue = document.getElementById('sortByValue').checked;
        const sortByQuantity = document.getElementById('sortByQuantity').checked;
        const sortBy = sortByValue ? 'value' : (sortByQuantity ? 'quantity' : 'value');

        // Get date range values
        const startDateInput = document.getElementById('start-date');
        const site_idselect = document.getElementById('site_idselect').value;
        const endDateInput = document.getElementById('end-date');
        const startDate = startDateInput ? startDateInput.value : '';
        const endDate = endDateInput ? endDateInput.value : '';
        const division = this.getAttribute('data-division');
        // Save settings via AJAX
        fetch('/superadmin/save-best-parts-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                limit: limit,
                sort_by: sortBy
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Instead of reloading the page, refresh the data via AJAX
            loadBestPartsData(division,site_idselect, startDate, endDate);
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            showErrorMessage('Gagal menyimpan pengaturan. Silakan periksa koneksi internet dan coba lagi.');
            hideBestPartsLoading();
        });
    });
}

/**
 * Format number with thousand separators
 *
 * @param {number} number - The number to format
 * @returns {string} - The formatted number
 */
function formatNumber(number) {
    return new Intl.NumberFormat('id-ID').format(Math.round(number));
}

/**
 * Show loading overlay
 */
function showLoadingOverlay() {
    const overlay = document.getElementById('skeleton-loader');
    const content = document.querySelector('.content-wrapper');

    if (overlay) overlay.style.display = 'block';
    if (content) content.style.display = 'none';
}

/**
 * Hide loading overlay
 */
function hideLoadingOverlay() {
    const overlay = document.getElementById('skeleton-loader');
    const content = document.querySelector('.content-wrapper');

    if (overlay) overlay.style.display = 'none';
    if (content) content.style.display = 'block';
}

/**
 * Show loading indicator specifically for best parts section
 */
function showBestPartsLoading() {
    // Add loading overlay to the best parts section
    const bestPartsSection = document.querySelector('.col-12.col-lg-8');
    if (bestPartsSection) {
        // Create loading overlay if it doesn't exist
        let loadingOverlay = bestPartsSection.querySelector('.best-parts-loading-overlay');
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'best-parts-loading-overlay';
            loadingOverlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(255, 255, 255, 0.9);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                border-radius: 10px;
            `;
            loadingOverlay.innerHTML = `
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">.</span>
                </div>
                <div class="text-center">
                    <h6 class="mb-1">Memuat Data Part Terbaik</h6>
                    <small class="text-muted">Mohon tunggu sebentar...</small>
                </div>
            `;
            bestPartsSection.style.position = 'relative';
            bestPartsSection.appendChild(loadingOverlay);
        } else {
            loadingOverlay.style.display = 'flex';
        }
    }

    // Also add loading state to category tabs
    const categoryTabs = document.querySelectorAll('.category-tab');
    categoryTabs.forEach(tab => {
        tab.style.opacity = '0.6';
        tab.style.pointerEvents = 'none';
    });

    // Add loading state to settings button
    const settingsBtn = document.getElementById('saveBestPartsSettings');
    if (settingsBtn) {
        settingsBtn.disabled = true;
        settingsBtn.style.opacity = '0.6';
    }
}

/**
 * Hide loading indicator for best parts section
 */
function hideBestPartsLoading() {
    // Remove loading overlay from best parts section
    const bestPartsSection = document.querySelector('.col-12.col-lg-8');
    if (bestPartsSection) {
        const loadingOverlay = bestPartsSection.querySelector('.best-parts-loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    // Restore category tabs
    const categoryTabs = document.querySelectorAll('.category-tab');
    categoryTabs.forEach(tab => {
        tab.style.opacity = '1';
        tab.style.pointerEvents = 'auto';
    });

    // Restore settings button
    const settingsBtn = document.getElementById('saveBestPartsSettings');
    if (settingsBtn) {
        settingsBtn.disabled = false;
        settingsBtn.style.opacity = '1';
    }
}

/**
 * Show success feedback
 */
function showSuccessFeedback() {
    // Create a subtle success indicator
    const bestPartsSection = document.querySelector('.col-12.col-lg-8 .card-title');
    if (bestPartsSection) {
        // Add a small success icon temporarily
        const successIcon = document.createElement('i');
        successIcon.className = 'mdi mdi-check-circle text-success ms-2';
        successIcon.style.fontSize = '16px';
        bestPartsSection.appendChild(successIcon);

        // Remove the icon after 2 seconds
        setTimeout(() => {
            if (successIcon.parentNode) {
                successIcon.parentNode.removeChild(successIcon);
            }
        }, 2000);
    }
}

/**
 * Show error message
 * @param {string} message - Error message to display
 */
function showErrorMessage(message) {
    // Create error toast or alert
    const errorAlert = document.createElement('div');
    errorAlert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    errorAlert.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    errorAlert.innerHTML = `
        <i class="mdi mdi-alert-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(errorAlert);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (errorAlert.parentNode) {
            errorAlert.remove();
        }
    }, 5000);
}
