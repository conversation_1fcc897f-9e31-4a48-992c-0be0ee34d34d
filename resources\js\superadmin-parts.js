/**
 * Superadmin Parts Page JavaScript
 *
 * Handles the functionality for the superadmin parts page
 */

/**
 * Format duration in days to a more readable format
 *
 * @param {number} days - Number of days
 * @return {string} Formatted duration string
 */

function formatDuration(days) {
    days = Number(days);
    if (days < 1) return "Hari ini";
    days = Math.floor(days);

    if (days === 1) return "1 hari";
    if (days < 7) return `${days} hari`;
    if (days === 7) return "1 minggu";
    if (days < 14) return `1 minggu ${Math.floor(days - 7)} hari`;
    if (days === 14) return "2 minggu";
    if (days < 21) return `2 minggu ${Math.floor(days - 14)} hari`;
    if (days === 21) return "3 minggu";
    if (days < 28) return `3 minggu ${Math.floor(days - 21)} hari`;
    if (days === 30) return "1 bulan";
    if (days > 30) {
        const months = Math.floor(days / 30);
        const remainingDays = Math.floor(days % 30);
        if (remainingDays === 0) return `${months} bulan`;
        return `${months} bulan ${remainingDays} hari`;
    }
    return `${days} hari`;
}

document.addEventListener("DOMContentLoaded", function () {
    // Register Chart.js plugins
    if (window.Chart) {
        // Make sure ChartDataLabels is registered
        if (window.ChartDataLabels) {
            Chart.register(ChartDataLabels);
        }
    }

    // Hide skeleton loader and show content
    setTimeout(function () {
        const skeletonLoader = document.getElementById("skeleton-loader");
        const contentWrapper = document.querySelector(".content-wrapper");

        if (skeletonLoader) {
            skeletonLoader.style.display = "none";
        }

        if (contentWrapper) {
            contentWrapper.style.display = "block";
        }

        // Initialize charts
        initializePartStatusCharts();
    }, 500);

    // Set up month navigation
    setupMonthNavigation();

    // Set up part status card click handlers
    setupPartStatusCardClickHandlers();

    // Set up penawaran detail button click handlers
    setupPenawaranDetailHandlers();

    // Initialize search functionality (if search input exists)
    const searchInput = document.getElementById("searchInput");
    if (searchInput) {
        initSearchFunctionality();
    }
});

function initializePartStatusCharts() {
    const partStatusDataElements =
        document.querySelectorAll(".part-status-data");
    if (partStatusDataElements.length === 0) {
        return;
    }

    // Create a chart for each site
    partStatusDataElements.forEach((dataElement) => {
        const siteId = dataElement.getAttribute("data-site-id");
        const readyCount =
            parseInt(dataElement.getAttribute("data-ready")) || 0;
        const notReadyCount =
            parseInt(dataElement.getAttribute("data-not-ready")) || 0;

        // Get the canvas element
        const canvas = document.getElementById(`part-status-chart-${siteId}`);

        if (canvas) {
            // Destroy existing chart if it exists
            if (window.partCharts && window.partCharts[siteId]) {
                window.partCharts[siteId].destroy();
            }

            // Create the chart
            try {
                // Initialize the charts object if it doesn't exist
                if (!window.partCharts) {
                    window.partCharts = {};
                }

                // Set canvas height to ensure proper sizing
                canvas.height = 80;

                // Create new chart
                window.partCharts[siteId] = new Chart(canvas, {
                    type: "doughnut",
                    data: {
                        labels: ["Not Ready", "Ready"],
                        datasets: [
                            {
                                data: [notReadyCount, readyCount],
                                backgroundColor: [
                                    "rgba(235, 49, 36, 0.8)", // Red (left side) - using the danger color #eb3124
                                    "rgba(40, 167, 69, 0.8)", // Green (right side)
                                ],
                                borderColor: [
                                    "rgba(235, 49, 36, 1)", // Red border
                                    "rgba(40, 167, 69, 1)", // Green border
                                ],
                                borderWidth: 1,
                            },
                        ],
                    },
                    options: {
                        rotation: 180,
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: "60%",
                        plugins: {
                            legend: {
                                position: "bottom",
                                labels: {
                                    padding: 10,
                                    boxWidth: 10,
                                    font: {
                                        size: 11,
                                    },
                                },
                            },
                            tooltip: {
                                callbacks: {
                                    label: function (context) {
                                        const label = context.label || "";
                                        const value = context.raw || 0;
                                        const total =
                                            context.dataset.data.reduce(
                                                (a, b) => a + b,
                                                0
                                            );
                                        const percentage =
                                            total > 0
                                                ? Math.round(
                                                      (value / total) * 100
                                                  )
                                                : 0;

                                        return `${label}: ${value} part (${percentage}%)`;
                                    },
                                },
                            },
                            datalabels: {
                                formatter: (value, ctx) => {
                                    const total = ctx.dataset.data.reduce(
                                        (a, b) => a + b,
                                        0
                                    );
                                    if (total === 0) return "";
                                    const percentage = Math.round(
                                        (value / total) * 100
                                    );
                                    return percentage + "%";
                                },
                                color: "#fff",
                                font: {
                                    weight: "bold",
                                    size: 12,
                                },
                                anchor: "center",
                                align: "center",
                                offset: 0,
                            },
                        },
                        animation: {
                            animateScale: true,
                            animateRotate: true,
                        },
                    },
                });
            } catch (error) {
                console.error("Error creating chart for site", siteId, error);
            }
        }
    });
}

/**
 * Set up month navigation buttons
 */
function setupMonthNavigation() {
    const monthPicker = document.getElementById("month-picker");
    const prevMonthBtn = document.getElementById("prevMonthBtn");
    const nextMonthBtn = document.getElementById("nextMonthBtn");

    if (monthPicker && prevMonthBtn && nextMonthBtn) {
        // Previous month button
        prevMonthBtn.addEventListener("click", function () {
            const currentDate = new Date(monthPicker.value + "-01");
            currentDate.setMonth(currentDate.getMonth() - 1);

            const year = currentDate.getFullYear();
            const month = String(currentDate.getMonth() + 1).padStart(2, "0");

            monthPicker.value = `${year}-${month}`;
            document.getElementById("monthForm").submit();
        });

        // Next month button
        nextMonthBtn.addEventListener("click", function () {
            const currentDate = new Date(monthPicker.value + "-01");
            currentDate.setMonth(currentDate.getMonth() + 1);

            const year = currentDate.getFullYear();
            const month = String(currentDate.getMonth() + 1).padStart(2, "0");

            monthPicker.value = `${year}-${month}`;
            document.getElementById("monthForm").submit();
        });

        // Month picker change
        monthPicker.addEventListener("change", function () {
            document.getElementById("monthForm").submit();
        });
    }
}

/**
 * Show loading overlay
 */
function showLoadingOverlay() {
    const skeletonLoader = document.getElementById("skeleton-loader");
    if (skeletonLoader) {
        skeletonLoader.style.display = "flex";
    }
}

/**
 * Hide loading overlay
 */
function hideLoadingOverlay() {
    const skeletonLoader = document.getElementById("skeleton-loader");
    if (skeletonLoader) {
        skeletonLoader.style.display = "none";
    }
}

/**
 * Refresh data via AJAX
 */
function refreshData() {
    showLoadingOverlay();

    const monthPicker = document.getElementById("month-picker");
    const selectedMonth = monthPicker ? monthPicker.value : "";

    // Fetch updated data
    fetch(`/superadmin/parts-data?month=${selectedMonth}`)
        .then((response) => {
            if (!response.ok) {
                throw new Error("Network response was not ok");
            }
            return response.json();
        })
        .then((data) => {
            // Update the requisition table
            updateRequisitionTable(data.requisitions);

            // Update the penawaran table
            updatePenawaranTable(data.penawarans);

            // Update the part status charts
            updatePartStatusCharts(data.site_parts_data);

            // Hide loading overlay
            hideLoadingOverlay();
        })
        .catch((error) => {
            alert("Gagal memuat data part. Silakan coba lagi.");
            hideLoadingOverlay();
        });
}

/**
 * Update requisition table with new data
 *
 * @param {Array} requisitions - Array of requisition data
 */
function updateRequisitionTable(requisitions) {
    const tableBody = document.getElementById("requisition-table-body");

    if (tableBody) {
        let html = "";

        if (requisitions.length > 0) {
            requisitions.forEach((requisition, index) => {
                // Determine status class
                let statusClass = "bg-secondary";
                switch (requisition.status) {
                    case "diajukan":
                        statusClass = "bg-warning text-dark";
                        break;
                    case "pending":
                        statusClass = "bg-info text-white";
                        break;
                    case "disetujui":
                        statusClass = "bg-primary text-white";
                        break;
                    case "selesai":
                        statusClass = "bg-success text-white";
                        break;
                }

                // Determine age display
                let ageDisplay = "-";
                if (requisition.age_days !== undefined) {
                    ageDisplay =
                        requisition.age_days < 1
                            ? '<span class="badge bg-info text-white">Hari ini</span>'
                            : `${requisition.age_days} hari`;
                } else if (requisition.age_in_days !== undefined) {
                    ageDisplay =
                        requisition.age_in_days === 0
                            ? '<span class="badge bg-info text-white">Hari ini</span>'
                            : `${requisition.age_in_days} hari`;
                }

                // Determine item count
                let itemCount = "-";
                if (requisition.details_count !== undefined) {
                    itemCount = requisition.details_count;
                } else if (requisition.item_count !== undefined) {
                    itemCount = requisition.item_count;
                }

                html += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${requisition.title}</td>
                    <td>${requisition.site_name}</td>
                    <td>
                        <span class="badge ${statusClass}">
                            ${
                                requisition.status.charAt(0).toUpperCase() +
                                requisition.status.slice(1)
                            }
                        </span>
                    </td>
                    <td>${requisition.created_at}</td>
                    <td>${ageDisplay}</td>
                    <td>${itemCount}</td>
                </tr>
                `;
            });
        } else {
            html =
                '<tr><td colspan="7" class="text-center">Tidak ada data pengajuan</td></tr>';
        }

        tableBody.innerHTML = html;
    }
}

/**
 * Update penawaran table with new data
 *
 * @param {Array} penawarans - Array of penawaran data
 */
function updatePenawaranTable(penawarans) {
    const tableBody = document.getElementById("penawaran-table-body");

    if (tableBody) {
        let html = "";

        if (penawarans && penawarans.length > 0) {
            penawarans.forEach((penawaran, index) => {
                // Determine status class
                let statusClass = "bg-secondary text-white";
                switch (penawaran.status) {
                    case "Draft":
                        statusClass = "bg-secondary text-white";
                        break;
                    case "Dikirim ke customer":
                        statusClass = "bg-info text-white";
                        break;
                    case "PO customer":
                        statusClass = "bg-primary text-white";
                        break;
                    case "Proses penyediaan":
                        statusClass = "bg-warning text-dark";
                        break;
                    case "Selesai":
                        statusClass = "bg-success text-white";
                        break;
                }

                html += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${penawaran.nomor}</td>
                    <td>${penawaran.customer}</td>
                    <td>${penawaran.tanggal}</td>
                    <td>
                        <span class="badge ${statusClass}">
                            ${penawaran.status}
                        </span>
                    </td>
                    <td>Rp ${formatNumber(penawaran.total)}</td>
                    <td>
                        <button class="btn btn-sm btn-primary btn-view-penawaran" data-id="${
                            penawaran.id
                        }">
                            <i class="mdi mdi-eye"></i> Detail
                        </button>
                    </td>
                </tr>
                `;
            });
        } else {
            html =
                '<tr><td colspan="7" class="text-center">Tidak ada data penawaran</td></tr>';
        }

        tableBody.innerHTML = html;
    }
}

/**
 * Format number with thousand separators
 *
 * @param {number} number - Number to format
 * @return {string} Formatted number
 */
function formatNumber(number) {
    return new Intl.NumberFormat("id-ID").format(number);
}

/**
 * Format date to Indonesian format (DD/MM/YYYY)
 *
 * @param {string} dateString - Date string to format
 * @return {string} Formatted date
 */
function formatDate(dateString) {
    if (!dateString) return "-";

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString; // Return original if invalid

        const day = String(date.getDate()).padStart(2, "0");
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
    } catch (error) {
        return dateString; // Return original on error
    }
}

/**
 * Set up penawaran detail button click handlers
 */
function setupPenawaranDetailHandlers() {
    // Delegate event handling to the document for dynamically added buttons
    document.addEventListener("click", function (event) {
        if (event.target.closest(".btn-view-penawaran")) {
            const button = event.target.closest(".btn-view-penawaran");
            const penawaranId = button.getAttribute("data-id");
            showPenawaranDetail(penawaranId);
        }
    });
}

/**
 * Show penawaran detail modal
 *
 * @param {number} penawaranId - ID of the penawaran to show
 */
function showPenawaranDetail(penawaranId) {
    // Get the modal
    const modal = document.getElementById("penawaranDetailModal");

    if (!modal) {
        return;
    }

    // Show loading state
    document.getElementById("detail-nomor").textContent = ".";
    document.getElementById("detail-perihal").textContent = ".";
    document.getElementById("detail-customer").textContent = ".";
    document.getElementById("detail-attn").textContent = ".";
    document.getElementById("detail-lokasi").textContent = ".";
    document.getElementById("detail-tanggal").textContent = ".";
    document.getElementById("detail-status").textContent = ".";
    document.getElementById("detail-notes").innerHTML = ".";
    document.getElementById("detail-parts-table-body").innerHTML =
        '<tr><td colspan="6" class="text-center">.</td></tr>';
    document.getElementById("detail-grand-total").textContent = ".";

    // Show the modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Fetch penawaran data from the superadmin endpoint
    fetch(`/superadmin/penawaran/${penawaranId}`)
        .then((response) => {
            if (!response.ok) {
                throw new Error("Network response was not ok");
            }
            return response.json();
        })
        .then((data) => {
            // Fill modal with penawaran data
            document.getElementById("detail-nomor").textContent = data.nomor;
            document.getElementById("detail-perihal").textContent =
                data.perihal;
            document.getElementById("detail-customer").textContent =
                data.customer;
            document.getElementById("detail-attn").textContent =
                data.attn || "-";
            document.getElementById("detail-lokasi").textContent = data.lokasi;

            // Use tanggal_penawaran if available, otherwise fall back to created_at
            let dateToDisplay = data.created_at;
            if (data.tanggal_penawaran) {
                dateToDisplay = data.tanggal_penawaran;
            }
            document.getElementById("detail-tanggal").textContent =
                formatDate(dateToDisplay);

            // Display status with badge
            let statusClass = "bg-secondary";
            switch (data.status) {
                case "Draft":
                    statusClass = "bg-secondary";
                    break;
                case "Dikirim ke customer":
                    statusClass = "bg-info";
                    break;
                case "PO customer":
                    statusClass = "bg-primary";
                    break;
                case "Proses penyediaan":
                    statusClass = "bg-warning";
                    break;
                case "Selesai":
                    statusClass = "bg-success";
                    break;
            }
            document.getElementById(
                "detail-status"
            ).innerHTML = `<span class="badge ${statusClass}">${data.status}</span>`;

            // Format and display notes
            let notesHtml = "";
            if (data.notes) {
                // Split notes by new line and create a list
                const noteLines = data.notes.split("\n");
                if (noteLines.length > 1) {
                    notesHtml = '<ul class="mb-0">';
                    noteLines.forEach((line) => {
                        if (line.trim()) {
                            notesHtml += `<li>${line}</li>`;
                        }
                    });
                    notesHtml += "</ul>";
                } else {
                    notesHtml = data.notes;
                }
            } else {
                notesHtml = "<em>Tidak ada catatan</em>";
            }
            document.getElementById("detail-notes").innerHTML = notesHtml;

            // Calculate part status counts and total
            let readyCount = 0;
            let inOrderCount = 0;
            let notReadyCount = 0;
            let grandTotal = 0;

            // Display parts
            if (data.items && data.items.length > 0) {
                let partsHtml = "";

                data.items.forEach((item, index) => {
                    // Count statuses
                    switch (item.status) {
                        case "Ready":
                            readyCount++;
                            break;
                        case "In Order":
                            inOrderCount++;
                            break;
                        case "Not Ready":
                            notReadyCount++;
                            break;
                    }

                    // Calculate item total
                    const itemTotal = item.quantity * item.price;
                    grandTotal += itemTotal;

                    // Determine status class
                    let itemStatusClass = "bg-secondary";
                    switch (item.status) {
                        case "Ready":
                            itemStatusClass = "bg-success";
                            break;
                        case "In Order":
                            itemStatusClass = "bg-info";
                            break;
                        case "Not Ready":
                            itemStatusClass = "bg-secondary";
                            break;
                    }

                    // Get part name
                    const partName =
                        item.part_inventory?.part?.part_name || "Unknown Part";

                    // Create responsive row with appropriate classes for mobile
                    partsHtml += `
                    <tr>
                        <td class="d-none d-md-table-cell">${index + 1}</td>
                        <td>${partName}</td>
                        <td>${item.quantity}</td>
                        <td class="text-end d-none d-sm-table-cell">Rp ${formatNumber(
                            item.price
                        )}</td>
                        <td class="text-end">Rp ${formatNumber(itemTotal)}</td>
                        <td><span class="badge ${itemStatusClass}">${
                        item.status
                    }</span></td>
                    </tr>
                    `;
                });

                document.getElementById("detail-parts-table-body").innerHTML =
                    partsHtml;
            } else {
                document.getElementById("detail-parts-table-body").innerHTML =
                    '<tr><td colspan="6" class="text-center">Tidak ada data part</td></tr>';
            }

            // Update grand total
            document.getElementById(
                "detail-grand-total"
            ).textContent = `Rp ${formatNumber(grandTotal)}`;
        })
        .catch((error) => {
            document.getElementById("detail-parts-table-body").innerHTML =
                '<tr><td colspan="6" class="text-center text-danger">Gagal memuat data. Silakan coba lagi.</td></tr>';
        });
}

/**
 * Update part status charts with new data
 *
 * @param {Object} sitePartsData - Object containing part status data for each site
 */
function updatePartStatusCharts(sitePartsData) {
    const partStatusDataElements =
        document.querySelectorAll(".part-status-data");
    partStatusDataElements.forEach((dataElement) => {
        const siteId = dataElement.getAttribute("data-site-id");

        if (sitePartsData[siteId]) {
            const readyCount = sitePartsData[siteId].parts_ready.count;
            const notReadyCount = sitePartsData[siteId].parts_not_ready.count;

            // Update data attributes
            dataElement.setAttribute("data-ready", readyCount);
            dataElement.setAttribute("data-not-ready", notReadyCount);

            // Update card counts
            const siteCard = dataElement.closest(".card");
            if (siteCard) {
                const readyCard = siteCard.querySelector(
                    ".part-status-card.ready h3"
                );
                const notReadyCard = siteCard.querySelector(
                    ".part-status-card.not-ready h3"
                );

                if (readyCard) {
                    readyCard.textContent = readyCount;
                }
                if (notReadyCard) {
                    notReadyCard.textContent = notReadyCount;
                }
            }

            // Update chart using window.partCharts if available
            if (window.partCharts && window.partCharts[siteId]) {
                window.partCharts[siteId].data.datasets[0].data = [
                    readyCount,
                    notReadyCount,
                ];
                window.partCharts[siteId].update();
            } else {
                // Fallback to Chart.getChart
                const canvas = document.getElementById(
                    `part-status-chart-${siteId}`
                );
                if (canvas) {
                    try {
                        const chart = Chart.getChart(canvas);
                        if (chart) {
                            chart.data.datasets[0].data = [
                                readyCount,
                                notReadyCount,
                            ];
                            chart.update();
                        } else {
                            if (!window.partCharts) {
                                window.partCharts = {};
                            }

                            // Set canvas height to ensure proper sizing
                            canvas.height = 4000;

                            window.partCharts[siteId] = new Chart(canvas, {
                                type: "doughnut",
                                data: {
                                    labels: ["Not Ready", "Ready"],
                                    datasets: [
                                        {
                                            data: [notReadyCount, readyCount],
                                            backgroundColor: [
                                                "rgba(235, 49, 36, 0.8)", // Red (left side) - using the danger color #eb3124
                                                "rgba(40, 167, 69, 0.8)", // Green (right side)
                                            ],
                                            borderColor: [
                                                "rgba(235, 49, 36, 1)", // Red border
                                                "rgba(40, 167, 69, 1)", // Green border
                                            ],
                                            borderWidth: 1,
                                        },
                                    ],
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: true,
                                    aspectRatio: 2,
                                    cutout: "60%",
                                    plugins: {
                                        legend: {
                                            position: "bottom",
                                            labels: {
                                                padding: 5,
                                                boxWidth: 10,
                                                font: {
                                                    size: 11,
                                                },
                                            },
                                        },
                                        tooltip: {
                                            callbacks: {
                                                label: function (context) {
                                                    const label =
                                                        context.label || "";
                                                    const value =
                                                        context.raw || 0;
                                                    const total =
                                                        context.dataset.data.reduce(
                                                            (a, b) => a + b,
                                                            0
                                                        );
                                                    const percentage =
                                                        total > 0
                                                            ? Math.round(
                                                                  (value /
                                                                      total) *
                                                                      100
                                                              )
                                                            : 0;
                                                    return `${label}: ${value} part (${percentage}%)`;
                                                },
                                            },
                                        },
                                        datalabels: {
                                            formatter: (value, ctx) => {
                                                const total =
                                                    ctx.dataset.data.reduce(
                                                        (a, b) => a + b,
                                                        0
                                                    );
                                                if (total === 0) return "";
                                                const percentage = Math.round(
                                                    (value / total) * 100
                                                );
                                                return percentage + "%";
                                            },
                                            color: "#fff",
                                            font: {
                                                weight: "bold",
                                                size: 12,
                                            },
                                            anchor: "center",
                                            align: "center",
                                            offset: 0,
                                        },
                                    },
                                    animation: {
                                        animateScale: true,
                                        animateRotate: true,
                                    },
                                },
                            });
                        }
                    } catch (error) {
                        console.error(
                            `Error updating chart for site ${siteId}:`,
                            error
                        );
                    }
                }
            }
        }
    });
}

function setupPartStatusCardClickHandlers() {
    // Get all part status cards
    const partStatusCards = document.querySelectorAll(".part-status-card");
    partStatusCards.forEach((card) => {
        card.addEventListener("click", function () {
            const siteId = this.getAttribute("data-site-id");
            const status = this.getAttribute("data-status");
            showPartStatusModal(siteId, status);
        });
    });
}

function showPartStatusModal(siteId, status) {
    // Get the modal
    const modal = document.getElementById("partStatusModal");
    const modalTitle = document.getElementById("partStatusModalLabel");
    const tableBody = document.getElementById("part-status-table-body");

    if (!modal || !modalTitle || !tableBody) {
        return;
    }
    tableBody.innerHTML =
        '<tr><td colspan="6" class="text-center py-3">Memuat data...</td></tr>';

    let siteName = "Unknown Site";
    // Find the site name from the card header
    const siteCard = document
        .querySelector(`.part-status-card[data-site-id="${siteId}"]`)
        .closest(".card");
    if (siteCard) {
        const cardTitle = siteCard.querySelector(".card-header .card-title");
        if (cardTitle) {
            siteName = cardTitle.textContent
                .trim()
                .replace("Status Part - ", "");
        }
    }
    modalTitle.textContent = `Daftar Part ${
        status === "ready" ? "Ready" : "Not Ready"
    } - ${siteName}`;

    // Show the modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Get the month
    const monthPicker = document.getElementById("month-picker");
    const selectedMonth = monthPicker ? monthPicker.value : "";

    // Fetch part data
    fetch(`/superadmin/parts-data?month=${selectedMonth}`)
        .then((response) => {
            if (!response.ok) {
                throw new Error("Network response was not ok");
            }
            return response.json();
        })
        .then((data) => {
            // Get parts data for the selected site and status
            const siteData = data.site_parts_data[siteId];
            if (!siteData) {
                throw new Error("Site data not found");
            }

            const parts =
                status === "ready"
                    ? siteData.parts_ready.items
                    : siteData.parts_not_ready.items;

            // Update table body
            if (parts.length > 0) {
                let html = "";
                parts.forEach((part, index) => {
                    html += `
                    <tr>
                        <td class="d-none d-md-table-cell">${index + 1}</td>
                        <td>${part.part_code || "-"}</td>
                        <td>${part.part_name}</td>
                        <td>${part.stock}</td>
                        <td class="d-none d-sm-table-cell">${
                            part.min_stock
                        }</td>
                        <td class="d-none d-sm-table-cell">${
                            part.max_stock
                        }</td>
                        <td class="d-none d-sm-table-cell">${
                            part.priority == 1 ? "Prioritas" : "-"
                        }</td>
                    </tr>
                    `;
                });
                tableBody.innerHTML = html;
            } else {
                tableBody.innerHTML =
                    '<tr><td colspan="6" class="text-center py-3">Tidak ada data part</td></tr>';
            }
        })
        .catch((error) => {
            tableBody.innerHTML =
                '<tr><td colspan="6" class="text-center text-danger py-3">Gagal memuat data. Silakan coba lagi.</td></tr>';
        });
}

/**
 * Debounce function to limit how often a function can be called
 * @param {Function} func - The function to debounce
 * @param {number} wait - The time to wait in milliseconds
 * @returns {Function} - The debounced function
 */
function debounce(func, wait) {
    let timeout;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, wait);
    };
}

/**
 * Initialize search functionality with AJAX
 */
function initSearchFunctionality() {
    const searchInput = document.getElementById("searchInput");
    const searchBox = document.querySelector(".search-box");
    const searchIcon = document.querySelector(".search-icon");
    const searchLoading = document.querySelector(".search-loading");

    if (!searchInput) return;

    // Create a separate loading indicator for the search box only
    const createSearchLoadingIndicator = () => {
        // Only modify the search box indicator, not the whole page
        if (searchBox) searchBox.classList.add("searching");
        if (searchIcon) searchIcon.style.display = "none";
        if (searchLoading) searchLoading.style.display = "block";
    };

    // Reset the search loading indicator
    const resetSearchLoadingIndicator = () => {
        if (searchBox) searchBox.classList.remove("searching");
        if (searchIcon) searchIcon.style.display = "block";
        if (searchLoading) searchLoading.style.display = "none";
    };

    // Function to update only the card counts without redrawing charts
    const updateCardCounts = (sitePartsData) => {
        // Update only the card counts, not the charts
        Object.keys(sitePartsData).forEach((siteId) => {
            const readyCard = document.querySelector(
                `.part-status-card.ready[data-site-id="${siteId}"] h3`
            );
            const notReadyCard = document.querySelector(
                `.part-status-card.not-ready[data-site-id="${siteId}"] h3`
            );

            if (readyCard) {
                readyCard.textContent = sitePartsData[siteId].parts_ready.count;
            }

            if (notReadyCard) {
                notReadyCard.textContent =
                    sitePartsData[siteId].parts_not_ready.count;
            }
        });
    };

    // Add input event with debounce
    searchInput.addEventListener(
        "input",
        debounce(function () {
            const searchValue = this.value.trim();

            // If search is empty, don't make a request
            if (searchValue === "") {
                resetSearchLoadingIndicator();
                return;
            }

            // Show only the search box loading indicator
            createSearchLoadingIndicator();

            // Get the month
            const monthPicker = document.getElementById("month-picker");
            const selectedMonth = monthPicker ? monthPicker.value : "";

            // Fetch data with search parameter
            fetch(
                `/superadmin/parts-data?month=${selectedMonth}&search=${searchValue}`
            )
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Network response was not ok");
                    }
                    return response.json();
                })
                .then((data) => {
                    // Update only the card counts, not the charts
                    updateCardCounts(data.site_parts_data);

                    // Reset search loading indicator
                    resetSearchLoadingIndicator();
                })
                .catch((error) => {
                    resetSearchLoadingIndicator();
                });
        }, 500)
    ); // Increased debounce time to 500ms for better performance

    // Add event listener for Enter key
    searchInput.addEventListener("keydown", function (e) {
        if (e.key === "Enter") {
            e.preventDefault();

            const searchValue = this.value.trim();

            // If search is empty, don't make a request
            if (searchValue === "") {
                resetSearchLoadingIndicator();
                return;
            }

            // Show only the search box loading indicator
            createSearchLoadingIndicator();

            // Get the month
            const monthPicker = document.getElementById("month-picker");
            const selectedMonth = monthPicker ? monthPicker.value : "";

            // Fetch data with search parameter
            fetch(
                `/superadmin/parts-data?month=${selectedMonth}&search=${searchValue}`
            )
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Network response was not ok");
                    }
                    return response.json();
                })
                .then((data) => {
                    // Update only the card counts, not the charts
                    updateCardCounts(data.site_parts_data);

                    // Reset search loading indicator
                    resetSearchLoadingIndicator();
                })
                .catch((error) => {
                    resetSearchLoadingIndicator();
                });
        }
    });
}

// PART PRIORITASS JS TAMPIL DISINI
/**
 * Superadmin Parts Page JavaScript - Priority Parts Table
 *
 * Handles the functionality for the priority parts table without jQuery
 */

document.addEventListener("DOMContentLoaded", function () {
    // Initialize priority parts functionality
    // initPriorityParts();
    loadPriorityPartsData();
});

document.body.addEventListener("click", function (e) {
    if (e.target && e.target.id === "saveBestPartsSettings") {
        loadPriorityPartsData();
    }
});

function loadPriorityPartsData() {
    const siteName = document.getElementById("siteidselect2").value;
    const limit = document.getElementById("jumlahentry").value;
    const tbody = document.getElementById("tbodyprioritas");

    // Show loading indicator
    if (tbody) {
        tbody.innerHTML =
            '<tr><td colspan="5" class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> Memuat data...</td></tr>';
    }

    // Build URL with parameters
    const url = new URL("/superadmin/parts-prioritas", window.location.origin);
    url.searchParams.append("siteidselect", siteName);
    url.searchParams.append("jumlahentry", limit);

    fetch(url)
        .then((response) => {
            if (!response.ok) {
                throw new Error("Network response was not ok");
            }
            return response.json();
        })
        .then((data) => {
            updatePriorityPartsTable(data.datapartprioritas);
        })
        .catch((error) => {
            console.error("Error:", error);
            if (tbody) {
                tbody.innerHTML =
                    '<tr><td colspan="5" class="text-center text-danger">Gagal memuat data. Silakan coba lagi.</td></tr>';
            }
        });
}

function updatePriorityPartsTable(parts) {
    const tbody = document.getElementById("tbodyprioritas");
    const thead = document.getElementById("theadprioritas");
    const siteSelect = document.getElementById("siteidselect2");

    if (!tbody || !thead || !siteSelect) return;

    const showSiteColumn = siteSelect.value === "";

    // Update THEAD sesuai kondisi
    let theadHtml = `
        <tr>
            ${showSiteColumn ? "<th>Site</th>" : ""}
            <th>Part Name</th>
            <th>MIN</th>
            <th>MAX</th>
            <th>STOCK</th>
            <th>Tanggal Di Prioritas</th>
        </tr>
    `;
    thead.innerHTML = theadHtml;

    // Update TBODY
    let html = "";

    if (parts && parts.length > 0) {
        parts.forEach((part) => {
            html += `
                <tr>
                    ${
                        showSiteColumn
                            ? `<td>${
                                  part.site?.name || part.site_id || "-"
                              }</td>`
                            : ""
                    }
                    <td>${part.part?.part_name || "-"}</td>
                    <td>${part.min_stock ?? "-"}</td>
                    <td>${part.max_stock ?? "-"}</td>
                    <td class="${
                        part.stock_quantity < part.min_stock
                            ? "text-danger fw-bold"
                            : ""
                    }">
                        ${part.stock_quantity ?? 0}
                    </td>
                    <td>${formatDate(part.date_priority)}</td>
                </tr>
            `;
        });
    } else {
        html = `<tr><td colspan="${
            showSiteColumn ? 6 : 5
        }" class="text-center">Tidak ada data part yang tersedia.</td></tr>`;
    }

    tbody.innerHTML = html;
}


document.addEventListener('click', function (e) {
    const target = e.target.closest('.requisition-row');

    if (!target) return; // Bukan target kita itu

    const requisitionId = target.getAttribute('data-id');

    if (!requisitionId) return;

    fetch(`/requisitions/${requisitionId}/detail`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('detail-title').textContent = data.requisition.title || '-';
            document.getElementById('detail-site').textContent = data.requisition.site_id || data.requisition.site.site_id;
            document.getElementById('detail-status').textContent = data.requisition.status || '-';
            document.getElementById('detail-created').textContent = data.requisition.created_at || '-';
            document.getElementById('detail-adminname').textContent = data.requisition.modified_by || '-';

            const itemsBody = document.getElementById('detail-items');
            itemsBody.innerHTML = '';

            if (data.details && data.details.length > 0) {
                data.details.forEach(detail => {
                    const row = `<tr>
                        <td>${detail.part?.part_code ?? details.part_code}</td>
                        <td>${detail.part?.part_name ?? '-'}</td>
                        <td>${detail.quantity}</td>
                        <td>${detail.quantity_send}</td>
                        <td>${detail.notes ?? detail.notes_ho ?? ''}</td>
                        <td>${detail.status_details ?? '-'}</td>
                    </tr>`;
                    itemsBody.insertAdjacentHTML('beforeend', row);
                });
            } else {
                itemsBody.innerHTML = '<tr><td colspan="3">Tidak ada detail</td></tr>';
            }

            const modal = new bootstrap.Modal(document.getElementById('detailModal'));
            modal.show();
        })
        .catch(error => {
            console.error(error);
            alert('Gagal mengambil detail.');
        });
});
