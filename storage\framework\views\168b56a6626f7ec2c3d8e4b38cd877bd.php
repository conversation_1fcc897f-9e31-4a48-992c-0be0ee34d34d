<?php $__env->startSection('title', 'Daftar Invoice'); ?>
<?php $__env->startSection('resourcesales'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/sales/invoices.js']); ?>
    <style>
        .w-fit-content {
            width: fit-content;
        }

        .shadow-kit {
            border: 1px solid rgb(42, 105, 168);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 0.5rem;
            background-color: #fff;
        }
        .dropdown-menu{
            z-index: 999999;
        }

        .btn {
            font-size: 14px;
        }

        .btn-warning {
            background-color: rgb(242, 215, 132);
            color: #000;
        }

        .bg-warning {
            background-color: rgb(240, 250, 150);
            color: #0f0187;
        }

        /* Invoice link styling */
        .document-link {
            color: #225297;
            text-decoration: underline;
            font-weight: 500;
            cursor: pointer;
        }

        .document-link:hover {
            color: #3a75d3;
            text-decoration: underline;
        }

        .sort-icon {
            font-size: 14px;
            margin-left: 5px;
            opacity: 0.5;
        }

        /* Search box styles */
        .search-box {
            position: relative;
            width: 200px;
        }

        .search-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #aaa;
        }

        /* Date range filter styles */
        .date-range-filter {
            display: flex;
            align-items: center;
        }

        .date-range-filter label {
            margin-right: 5px;
            white-space: nowrap;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('contentsales'); ?>
    <?php echo $__env->make('sales.partials.navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-4">
                    <div class="px-4 py-2 bgwhite h3  rounded-lg font-bold">
                        TOTAL INVOICE : <span id="totalinvoice"></span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="bgwhite shadow-kit rounded-lg">
                        <div
                            class="card-header bg-primary text-white d-flex justify-content-between align-items-center pt-2 pb-1 pl-2 pr-2">
                            <div class="d-flex align-items-center">
                                <h5 class="mb-0 mr-3 font-bold text-uppercase text-white">Daftar Invoice</h5>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3 d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="date-range-filter mr-2">
                                        <div class="d-flex">
                                            <div class="mr-2">
                                                <label for="date-from" class="small mb-0">Dari Tanggal:</label>
                                                <input type="date" id="date-from" class="form-control form-control-sm"
                                                    value="<?php echo e($dateFrom ?? ''); ?>">
                                            </div>
                                            <div>
                                                <label for="date-to" class="small mb-0">Sampai Tanggal:</label>
                                                <input type="date" id="date-to" class="form-control form-control-sm"
                                                    value="<?php echo e($dateTo ?? ''); ?>">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="mr-3">
                                        <select class="btn btn-sm btn-primary" name="customer" id="customer">
                                        </select>
                                    </div>
                                    <div class="mr-3">
                                        <select class="btn btn-sm btn-primary" name="lengthpagenation"
                                            id="lengthpagenation">
                                            <option value="10" selected>Jumlah Data</option>
                                            <option value="15">15</option>
                                            <option value="20">20</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                            <option value="2000">Semua</option>
                                        </select>
                                    </div>
                                    <div class="search-box mr-2">
                                        <div class="position-relative">
                                            <input type="text" id="search-input" class="form-control form-control-sm"
                                                placeholder="Cari...">
                                            <i class="mdi mdi-magnify search-icon"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered w-100" id="invoices-table" style="font-size: 14px;">
                                    <thead class="bg-light">
                                        <tr>
                                            <th>TANGGAL</th>
                                            <th>TERMIN</th>
                                            <th>JATUH TEMPO</th>
                                            <th style="text-decoration: none;">INVOICE</th>
                                            <th>CUSTOMER</th>
                                            <th>KATEGORI</th>
                                            <th>NO. PO</th>
                                            <th>SN</th>
                                            <th>KETERANGAN</th>
                                            <th>NILAI INVOICE</th>
                                            <th>TAX</th>
                                            <th>NILAI AFTER TAX</th>
                                            <th>STATUS</th>
                                            <th>Doc</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody id="invoices-table-body">
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-1">
                                <div>
                                    <span id="showing-text">Menampilkan 0 dari 0 invoice</span>
                                </div>
                                <div>
                                    <nav aria-label="Page navigation">
                                        <ul class="pagination pagination-sm" id="pagination">
                                            <!-- Pagination will be generated dynamically -->
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Viewer Modal -->
    <div class="modal fade" id="document-viewer-modal" tabindex="-1" role="dialog"
        aria-labelledby="document-viewer-modal-label" aria-hidden="true">
        <div class="modal-dialog" role="document" style="max-width: 100%; margin: 0;">
            <div class="modal-content h-100">
                <div class="modal-header">
                    <h5 class="modal-title" id="document-viewer-modal-label">Dokumen Invoice</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body p-0" style="height: 90vh;">
                    <div id="document-viewer" class="h-100"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">Tutup</button>
                    <a id="download-document" href="#" class="btn btn-sm btn-primary" target="_blank">Download</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Direct Invoice Modal -->
    <div class="modal fade" id="direct-invoice-modal" tabindex="-1" role="dialog"
        aria-labelledby="direct-invoice-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="direct-invoice-modal-label">Tambah Invoice Langsung</h5>
                    <button type="button" class="btn-close btn-close-white close-modal-btn" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="direct-invoice-form" enctype="multipart/form-data">
                        <input type="hidden" id="invoice_id" name="invoice_id" value="">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="no_invoice" class="form-label">Nomor Invoice</label>
                                    <input type="text" class="form-control" id="no_invoice" name="no_invoice"
                                        placeholder="Nomor Invoice">
                                    <small class="text-muted">Format: 001/INV-PWB/MM/YYYY</small>
                                </div>
                                <div class="mb-3">
                                    <label for="customer" class="form-label">Customer</label>
                                    <input type="text" class="form-control" id="customeredit" name="customeredit"
                                        placeholder="Nama Customer">
                                </div>
                                <input type="hidden" id="site_id" name="site_id" value="">
                                <div class="mb-3">
                                    <label for="unit" class="form-label">Unit</label>
                                    <input type="text" class="form-control" id="unit" name="unit" placeholder="Kode Unit">
                                </div>
                                <div class="mb-3">
                                    <label for="po_number" class="form-label">Nomor PO</label>
                                    <input type="text" class="form-control" id="po_number" name="po_number"
                                        placeholder="Nomor PO">
                                </div>
                                <div class="mb-3">
                                    <label for="model_unit" class="form-label">Model Unit</label>
                                    <input type="text" class="form-control" id="model_unit" name="model_unit"
                                        placeholder="Model Unit">
                                </div>
                                <div class="mb-3">
                                    <label for="hmkm" class="form-label">HM/KM</label>
                                    <input type="text" class="form-control" id="hmkm" name="hmkm" placeholder="HM/KM">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tanggal_invoice" class="form-label">Tanggal Invoice</label>
                                    <input type="date" class="form-control" id="tanggal_invoice" name="tanggal_invoice">
                                </div>
                                <div class="mb-3">
                                    <label for="due_date" class="form-label">Tanggal Jatuh Tempo</label>
                                    <input type="date" class="form-control" id="due_date" name="due_date">
                                </div>
                                <div class="mb-3">
                                    <label for="sn" class="form-label">Serial Number</label>
                                    <input type="text" class="form-control" id="sn" name="sn" placeholder="Serial Number">
                                </div>
                                <div class="mb-3">
                                    <label for="trouble" class="form-label">Kategori</label>
                                    <input type="text" class="form-control" id="trouble" name="trouble"
                                        placeholder="Kategori">
                                </div>
                                <div class="mb-3">
                                    <label for="lokasi" class="form-label">Lokasi</label>
                                    <input type="text" class="form-control" id="lokasi" name="lokasi" placeholder="Lokasi">
                                </div>
                                <div class="mb-3">
                                    <label for="subtotal" class="form-label">Subtotal (Rp)</label>
                                    <input type="number" class="form-control" id="subtotal" name="subtotal"
                                        placeholder="Subtotal">
                                </div>
                                <div class="mb-3">
                                    <label for="ppn" class="form-label">PPN (%)</label>
                                    <input type="number" class="form-control" id="ppn" name="ppn" value="11"
                                        placeholder="PPN">
                                    <small class="text-muted">Masukkan dalam bentuk persentase (contoh: 11 untuk
                                        11%)</small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="transfer_to" class="form-label">Ditransfer Ke</label>
                                    <input type="text" class="form-control" id="transfer_to" name="transfer_to"
                                        placeholder="Ditransfer Ke">
                                </div>
                                <div class="mb-3">
                                    <label for="bank_account" class="form-label">Nomor Rekening</label>
                                    <input type="text" class="form-control" id="bank_account" name="bank_account"
                                        placeholder="Nomor Rekening">
                                </div>
                                <div class="mb-3">
                                    <label for="bank_branch" class="form-label">Cabang Bank</label>
                                    <input type="text" class="form-control" id="bank_branch" name="bank_branch"
                                        placeholder="Cabang Bank">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">Catatan</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"
                                        placeholder="Catatan"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="document" class="form-label">Dokumen Invoice</label>
                                    <input type="file" class="form-control" id="document" name="document"
                                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                    <small class="text-muted">Maksimal 10MB (PDF, JPG, JPEG, PNG, DOC, DOCX)</small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-secondary close-modal-btn"
                        data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-sm btn-primary" id="save-direct-invoice-btn">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Details Modal -->
    <div class="modal fade" id="invoiceDetailsModal" tabindex="-1" role="dialog" aria-labelledby="invoiceDetailsModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="invoiceDetailsModalLabel">Detail Invoice</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="invoice-details-content">
                    <!-- Invoice details will be loaded dynamically -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- Modal untuk memilih tanggal pelunasan -->
    <div class="modal fade" id="paymentDateModal" tabindex="-1" role="dialog" aria-labelledby="paymentDateModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentDateModalLabel">Pilih Tanggal Pelunasan</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="paymentDateInput">Tanggal Pelunasan</label>
                        <input type="date" class="form-control" id="paymentDateInput">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" id="confirmPaymentDate">Konfirmasi</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Add modal script -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Add event listeners to close modal buttons
            document.querySelectorAll('.close-modal-btn, [data-dismiss="modal"]').forEach(button => {
                button.addEventListener('click', function () {
                    const modalId = this.closest('.modal').id;
                    const modal = document.getElementById(modalId);
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                    document.body.classList.remove('modal-open');

                    // Remove backdrop
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                });
            });


        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('sales.contentsales', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/sales/invoices.blade.php ENDPATH**/ ?>